#!/bin/bash

# Script pour exécuter tous les tests de sécurité SIGMA (v1.2 - Final)
# Utilise Firebase Emulator Suite pour les tests isolés et force le bon projet ID.

set -e # Arrêter en cas d'erreur

# --- Configuration ---
FUNCTIONS_DIR="src/firebase/functions"
PROJECT_ID="sigma-nova" # Assurez-vous que c'est le bon ID de votre projet Firebase
EMULATOR_TIMEOUT=45 # Augmentation du timeout pour plus de robustesse
EMULATOR_PID="" # Variable pour stocker l'ID du processus des émulateurs

# --- Couleurs pour l'affichage ---
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# --- Fonctions d'Affichage ---
log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

# --- Fonction de Nettoyage ---
cleanup() {
    log_info "Nettoyage des processus..."
    if [ -n "$EMULATOR_PID" ]; then
        # Tuer le processus des émulateurs s'il existe
        kill $EMULATOR_PID > /dev/null 2>&1 || true
        log_success "Émulateurs arrêtés."
    else
        log_warning "Aucun processus d'émulateur à arrêter."
    fi
    # Attendre un peu pour que les processus se terminent
    sleep 2
    log_success "Nettoyage terminé"
}
trap cleanup EXIT INT TERM

# --- Vérifications Initiales ---
log_info "Vérification de l'environnement..."
if [ ! -f "firebase.json" ]; then
    log_error "Ce script doit être exécuté depuis la racine du projet SIGMA"
    exit 1
fi
if [ ! -d "$FUNCTIONS_DIR" ]; then
    log_error "Le dossier des fonctions '$FUNCTIONS_DIR' n'a pas été trouvé."
    exit 1
fi

# --- Début du Processus ---
echo "🧪 Démarrage de la suite complète de tests SIGMA"
echo "================================================"

# Étape 1: Installation des dépendances des Cloud Functions
log_info "Installation des dépendances..."
cd "$FUNCTIONS_DIR"
npm install
cd - > /dev/null # Retourne au répertoire précédent sans afficher le chemin
log_success "Dépendances Cloud Functions installées"

# Étape 2: Build des Cloud Functions
log_info "Build des Cloud Functions..."
cd "$FUNCTIONS_DIR"
npm run build
cd - > /dev/null
log_success "Cloud Functions buildées"

# Étape 3: Démarrage des émulateurs
log_info "Démarrage des émulateurs Firebase pour le projet '$PROJECT_ID'..."
firebase emulators:start --project $PROJECT_ID --only auth,firestore,functions &
EMULATOR_PID=$! # Capture de l'ID du processus

# Attendre que les émulateurs soient prêts
log_info "Attente du démarrage des émulateurs (max ${EMULATOR_TIMEOUT}s)..."
npx wait-on http://127.0.0.1:4400 -t ${EMULATOR_TIMEOUT}000 && log_success "Émulateurs prêts" || (log_error "Timeout: Les émulateurs ne sont pas prêts" && exit 1)

# Étape 4: Exécution des tests
log_info "Exécution de la suite de tests (Jest) sur l'environnement '$PROJECT_ID'..."
cd "$FUNCTIONS_DIR"

# Forcer les variables d'environnement pour que les tests se connectent correctement aux émulateurs
if GCLOUD_PROJECT=$PROJECT_ID FIREBASE_CONFIG={} npm test; then
    log_success "Tous les tests Jest ont réussi"
else
    log_error "Échec des tests Jest"
    exit 1
fi
cd - > /dev/null

# Étape 5: Résumé final
echo ""
echo "🎉 TOUS LES TESTS SONT RÉUSSIS !"
echo "================================"
log_success "L'Epic E-1 'Sécurité & Authentification' a été validé avec succès par les tests automatisés ! 🚀"

exit 0