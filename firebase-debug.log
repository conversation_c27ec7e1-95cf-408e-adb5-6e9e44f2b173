[debug] [2025-08-04T12:30:04.281Z] ----------------------------------------------------------------------
[debug] [2025-08-04T12:30:04.287Z] Command:       C:\nvm4w\nodejs\node.exe C:\nvm4w\nodejs\node_modules\firebase-tools\lib\bin\firebase.js emulators:start --only auth,firestore,functions
[debug] [2025-08-04T12:30:04.287Z] CLI Version:   14.11.2
[debug] [2025-08-04T12:30:04.288Z] Platform:      win32
[debug] [2025-08-04T12:30:04.288Z] Node Version:  v20.19.4
[debug] [2025-08-04T12:30:04.288Z] Time:          Mon Aug 04 2025 14:30:04 GMT+0200 (heure d’été d’Europe centrale)
[debug] [2025-08-04T12:30:04.288Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-08-04T12:30:04.815Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-08-04T12:30:04.816Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-08-04T12:30:05.074Z] openjdk version "17.0.14" 2025-01-21

[debug] [2025-08-04T12:30:05.076Z] OpenJDK Runtime Environment Temurin-17.0.14+7 (build 17.0.14+7)
OpenJDK 64-Bit Server VM Temurin-17.0.14+7 (build 17.0.14+7, mixed mode, sharing)

[debug] [2025-08-04T12:30:05.100Z] Parsed Java major version: 17
[info] i  emulators: Starting emulators: auth, functions, firestore {"metadata":{"emulator":{"name":"hub"},"message":"Starting emulators: auth, functions, firestore"}}
[debug] [2025-08-04T12:30:05.126Z] [logging] Logging Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-08-04T12:30:05.126Z] [auth] Authentication Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-08-04T12:30:05.126Z] [firestore] Firestore Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-08-04T12:30:05.127Z] [firestore.websocket] websocket server for firestore only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-08-04T12:30:05.127Z] assigned listening specs for emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}],"auth":[{"address":"127.0.0.1","family":"IPv4","port":9099}],"firestore":[{"address":"127.0.0.1","family":"IPv4","port":8080}],"firestore.websocket":[{"address":"127.0.0.1","family":"IPv4","port":9150}]},"metadata":{"message":"assigned listening specs for emulators"}}
[debug] [2025-08-04T12:30:05.134Z] [hub] writing locator at C:\Users\<USER>\AppData\Local\Temp\hub-sigma-nova.json
[debug] [2025-08-04T12:30:05.158Z] [functions] Functions Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-08-04T12:30:05.159Z] [eventarc] Eventarc Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-08-04T12:30:05.159Z] [tasks] Cloud Tasks Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-08-04T12:30:05.159Z] late-assigned ports for functions and eventarc emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}],"auth":[{"address":"127.0.0.1","family":"IPv4","port":9099}],"firestore":[{"address":"127.0.0.1","family":"IPv4","port":8080}],"firestore.websocket":[{"address":"127.0.0.1","family":"IPv4","port":9150}],"functions":[{"address":"127.0.0.1","family":"IPv4","port":5001}],"eventarc":[{"address":"127.0.0.1","family":"IPv4","port":9299}],"tasks":[{"address":"127.0.0.1","family":"IPv4","port":9499}]},"metadata":{"message":"late-assigned ports for functions and eventarc emulators"}}
[warn] !  functions: The following emulators are not running, calls to these services from the Functions emulator will affect production: apphosting, database, hosting, pubsub, storage, dataconnect {"metadata":{"emulator":{"name":"functions"},"message":"The following emulators are not running, calls to these services from the Functions emulator will affect production: \u001b[1mapphosting, database, hosting, pubsub, storage, dataconnect\u001b[22m"}}
[debug] [2025-08-04T12:30:05.196Z] defaultcredentials: writing to file C:\Users\<USER>\AppData\Roaming\firebase\aymericri974_gmail_com_application_default_credentials.json
[debug] [2025-08-04T12:30:05.201Z] Setting GAC to C:\Users\<USER>\AppData\Roaming\firebase\aymericri974_gmail_com_application_default_credentials.json {"metadata":{"emulator":{"name":"functions"},"message":"Setting GAC to C:\\Users\\<USER>\\AppData\\Roaming\\firebase\\aymericri974_gmail_com_application_default_credentials.json"}}
[debug] [2025-08-04T12:30:05.203Z] Checked if tokens are valid: true, expires at: 1754312313373
[debug] [2025-08-04T12:30:05.203Z] Checked if tokens are valid: true, expires at: 1754312313373
[debug] [2025-08-04T12:30:05.204Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/sigma-nova/adminSdkConfig [none]
[debug] [2025-08-04T12:30:05.650Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/sigma-nova/adminSdkConfig 200
[debug] [2025-08-04T12:30:05.651Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/sigma-nova/adminSdkConfig {"projectId":"sigma-nova","databaseURL":"https://sigma-nova-default-rtdb.europe-west1.firebasedatabase.app","storageBucket":"sigma-nova.firebasestorage.app"}
[debug] [2025-08-04T12:30:05.706Z] Ignoring unsupported arg: auto_download {"metadata":{"emulator":{"name":"firestore"},"message":"Ignoring unsupported arg: auto_download"}}
[debug] [2025-08-04T12:30:05.707Z] Ignoring unsupported arg: single_project_mode_error {"metadata":{"emulator":{"name":"firestore"},"message":"Ignoring unsupported arg: single_project_mode_error"}}
[debug] [2025-08-04T12:30:05.707Z] Starting Firestore Emulator with command {"binary":"java","args":["-Dgoogle.cloud_firestore.debug_log_level=FINE","-Duser.language=en","-jar","C:\\Users\\<USER>\\.cache\\firebase\\emulators\\cloud-firestore-emulator-v1.19.8.jar","--host","127.0.0.1","--port",8080,"--websocket_port",9150,"--project_id","sigma-nova","--rules","C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\firestore.rules","--single_project_mode",true,"--functions_emulator","127.0.0.1:5001"],"optionalArgs":["port","webchannel_port","host","rules","websocket_port","functions_emulator","seed_from_export","project_id","single_project_mode"],"joinArgs":false,"shell":false,"port":8080} {"metadata":{"emulator":{"name":"firestore"},"message":"Starting Firestore Emulator with command {\"binary\":\"java\",\"args\":[\"-Dgoogle.cloud_firestore.debug_log_level=FINE\",\"-Duser.language=en\",\"-jar\",\"C:\\\\Users\\\\<USER>\\\\.cache\\\\firebase\\\\emulators\\\\cloud-firestore-emulator-v1.19.8.jar\",\"--host\",\"127.0.0.1\",\"--port\",8080,\"--websocket_port\",9150,\"--project_id\",\"sigma-nova\",\"--rules\",\"C:\\\\Users\\\\<USER>\\\\Documents\\\\SIGMA-AGENT\\\\firestore.rules\",\"--single_project_mode\",true,\"--functions_emulator\",\"127.0.0.1:5001\"],\"optionalArgs\":[\"port\",\"webchannel_port\",\"host\",\"rules\",\"websocket_port\",\"functions_emulator\",\"seed_from_export\",\"project_id\",\"single_project_mode\"],\"joinArgs\":false,\"shell\":false,\"port\":8080}"}}
[info] i  firestore: Firestore Emulator logging to firestore-debug.log {"metadata":{"emulator":{"name":"firestore"},"message":"Firestore Emulator logging to \u001b[1mfirestore-debug.log\u001b[22m"}}
